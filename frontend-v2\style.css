:root {
  --primary-glow: rgba(56, 189, 248, 0.5);
  --primary-border: rgba(56, 189, 248, 0.2);
  --background-dark: #050816;
  --text-main: #e2e8f0;
  --text-secondary: #94a3b8;
  --text-accent: #38bdf8;
  --glass-bg: rgba(15, 23, 42, 0.75);
}

.light-theme {
  --primary-glow: rgba(14, 165, 233, 0.4);
  --primary-border: rgba(14, 165, 233, 0.3);
  --background-dark: #f8fafc;
  --text-main: #0f172a;
  --text-secondary: #475569;
  --text-accent: #0284c7;
  --glass-bg: rgba(255, 255, 255, 0.8);
  
  /* Additional colors for light theme */
  --card-bg: #ffffff;
  --card-shadow: rgba(0, 0, 0, 0.05);
  --hover-bg: rgba(14, 165, 233, 0.1);
  --border-subtle: #e2e8f0;
  --highlight: #f0f9ff;
}

body {
  font-family: "Inter", sans-serif;
  background-color: var(--background-dark);
  color: var(--text-main);
  overflow-x: hidden;
  transition: background-color 0.5s ease, color 0.5s ease;
}

.font-orbitron {
  font-family: "Orbitron", sans-serif;
}

#hero-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.glass-pane {
  background: #0009;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.glass-pane:hover {
  border-color: rgba(56, 189, 248, 0.5);
  box-shadow: 0 0 20px rgba(56, 189, 248, 0.2);
}

/* Recommendations Section Styles */
.recommendation-card {
  position: relative;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(56, 189, 248, 0.2);
}

.recommendation-avatar {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1), rgba(14, 165, 233, 0.1));
  border: 2px solid rgba(56, 189, 248, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.recommendation-card:hover .recommendation-avatar {
  border-color: rgba(56, 189, 248, 0.6);
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.2), rgba(14, 165, 233, 0.2));
}

.recommendation-quote-icon {
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.recommendation-card:hover .recommendation-quote-icon {
  opacity: 0.6;
}

/* Light theme adjustments for recommendations */
.light-theme .recommendation-card {
  background: var(--card-bg);
  border-color: var(--border-subtle);
}

.light-theme .recommendation-card:hover {
  border-color: var(--text-accent);
  box-shadow: 0 10px 30px var(--card-shadow);
}

.light-theme .recommendation-avatar {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(59, 130, 246, 0.1));
  border-color: var(--text-accent);
}

.light-theme .recommendation-card:hover .recommendation-avatar {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(59, 130, 246, 0.2));
}

/* Enhanced Bottom Dock Navigation */
#bottom-nav {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

@media (max-width: 767px) {
  #bottom-nav {
    left: 1rem;
    transform: none;
    width: auto;
  }
}

#bottom-dock {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70px;
  z-index: 1000;
  padding: 0 1.5rem;
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 40px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5),
              0 0 30px rgba(56, 189, 248, 0.3);
  transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  position: relative;
  overflow: visible;
}

.dock-item {
  color: white;
  transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1),
              left 0.2s cubic-bezier(0.4, 0.0, 0.2, 1),
              box-shadow 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  min-width: 50px;
  min-height: 50px;
  font-size: 1.25rem;
  background: rgba(15, 23, 42, 0.7);
  border-radius: 50%;
  cursor: pointer;
  z-index: 1000;
  transform-origin: center;
}

.dock-item:hover {
  color: var(--text-accent);
}

/* Language switcher specific styles */
.dock-item .lang-text {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 1px;
  text-shadow: 0 0 4px rgba(56, 189, 248, 0.5);
  color: white;
  transition: all 0.3s ease;
}

.dock-item:hover .lang-text {
  text-shadow: 0 0 8px rgba(56, 189, 248, 0.8);
  color: #38bdf8;
}

/* Dock separator */
.dock-separator {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.dock-item i {
  transition: color 0.3s ease;
}

/* Add a subtle glow effect to the dock */
#bottom-dock::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 15px;
  background: radial-gradient(ellipse at center, rgba(56, 189, 248, 0.3) 0%, rgba(56, 189, 248, 0) 70%);
  border-radius: 50%;
  z-index: -1;
}

/* Add a subtle bounce effect when clicking dock items */
@keyframes dockClick {
  0% { transform: scale(1); }
  50% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

.dock-click {
  animation: dockClick 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Mobile Dock */
#mobile-dock-toggle {
  display: none;
  z-index: 1000;
  color: white;
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 50%;
  transition: all 0.3s ease;
}

#mobile-dock-toggle:hover {
  color: var(--text-accent);
  border-color: var(--text-accent);
  box-shadow: 0 0 15px rgba(56, 189, 248, 0.4);
}

#mobile-dock-menu {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: auto;
  transform: none;
  flex-direction: column-reverse;
  gap: 0.8rem;
  margin-bottom: 1rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform-origin: bottom left;
  transform: scale(0.95);
  z-index: 999;
}

#mobile-dock-menu.open {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

#mobile-dock-menu .dock-item {
  margin-bottom: 0.5rem;
}

@media (max-width: 767px) {
  #bottom-dock {
    display: none;
  }
  #mobile-dock-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    cursor: pointer;
  }
}

/* Section Reveal Animation */
.reveal-section {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-section.hidden {
  opacity: 0;
  transform: translateY(20px);
}

.feature-section:nth-child(even) .feature-content {
  order: 2;
}

.pipeline-visual {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 1rem;
}
.pipeline-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--text-secondary);
  font-size: 1.5rem;
}
@media (min-width: 768px) {
  .pipeline-stage {
    font-size: 2.5rem;
  }
}
.pipeline-connector {
  flex-grow: 1;
  height: 2px;
  background: linear-gradient(
    to right,
    var(--primary-border),
    var(--primary-glow),
    var(--primary-border)
  );
  margin: 0 0.5rem;
  margin-bottom: 2rem;
}
@media (min-width: 768px) {
  .pipeline-connector {
    margin: 0 1rem;
    margin-bottom: 2.5rem;
  }
}

.form-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--primary-border);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text-main);
  transition: border-color 0.3s, background-color 0.5s;
}
.form-input:focus {
  outline: none;
  border-color: var(--primary-glow);
}

#photography-carousel {
  position: relative;
}
#carousel-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}
.carousel-item {
  flex: 0 0 100%;
}
@media (min-width: 768px) {
  .carousel-item {
    flex: 0 0 50%;
  }
}
@media (min-width: 1024px) {
  .carousel-item {
    flex: 0 0 33.333%;
  }
}
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--glass-bg);
  color: var(--text-main);
  border: 1px solid var(--primary-border);
  border-radius: 9999px;
  width: 3rem;
  height: 3rem;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s;
  display: none;
}
@media (min-width: 768px) {
  .carousel-btn {
    display: initial;
  }
}
.carousel-btn:hover {
  background-color: rgba(56, 189, 248, 0.2);
}
#prev-btn {
  left: -1.5rem;
}
#next-btn {
  right: -1.5rem;
}

.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.lightbox.show {
  opacity: 1;
  visibility: visible;
}
.lightbox img {
  max-width: 90%;
  max-height: 80%;
  border-radius: 0.5rem;
}
.lightbox-close {
  position: absolute;
  top: 2rem;
  right: 2rem;
  font-size: 3rem;
  color: #fff;
  cursor: pointer;
  line-height: 1;
}

/* Light Theme Text Overrides */
.light-theme h1,
.light-theme h2,
.light-theme h3 {
  color: var(--text-main);
}
.light-theme p,
.light-theme div,
.light-theme span {
  color: var(--text-secondary);
}
.light-theme .text-sky-300 {
  color: var(--text-accent);
}
.light-theme .text-white {
  color: var(--text-main);
}
.light-theme .text-slate-300 {
  color: var(--text-secondary);
}
.light-theme .text-slate-400 {
  color: var(--text-secondary);
}
.light-theme .text-slate-500 {
  color: #9ca3af;
}
.light-theme .hover\:text-sky-400:hover,
.light-theme .hover\:text-sky-300:hover {
  color: var(--text-accent);
}

/* Photography Swiper Styles */
#photography .swiper {
  width: 100%;
  padding-top: 3.125rem;
  padding-bottom: 3.125rem;
}

#photography .swiper-pagination-bullet,
#photography .swiper-pagination-bullet-active {
  background: var(--text-main);
}

#photography .swiper-pagination {
  bottom: 1.25rem !important;
}

#photography .swiper-slide {
  width: 18.75rem;
  height: 28.125rem;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  transition: transform 0.5s;
}

#photography .swiper-slide h2 {
  color: var(--text-main);
  font-family: "Orbitron", sans-serif;
  font-weight: 400;
  font-size: 1.4rem;
  line-height: 1.4;
  margin-bottom: 0.625rem;
  padding: 0 0 0 1.563rem;
  text-transform: uppercase;
}

#photography .swiper-slide p {
  color: var(--text-secondary);
  font-weight: 300;
  padding: 0 1.563rem;
  line-height: 1.6;
  font-size: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

#photography .swiper-slide a {
  margin: 1.25rem 1.563rem 3.438rem 1.563rem;
  padding: 0.438em 1.875rem;
  font-size: 0.9rem;
  background: var(--glass-bg);
  color: var(--text-accent);
  border: 1px solid var(--primary-border);
  border-radius: 2rem;
  transition: all 0.3s ease;
}

#photography .swiper-slide a:hover {
  background: rgba(56, 189, 248, 0.2);
  transform: translateY(-2px);
}

#photography .swiper-slide div {
  display: none;
  opacity: 0;
  padding-bottom: 0.625rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  width: 100%;
  transition: opacity 0.5s ease;
}

#photography .swiper-slide-active div {
  display: block;
  opacity: 1;
}

#photography .swiper-3d .swiper-slide-shadow-left,
#photography .swiper-3d .swiper-slide-shadow-right {
  background-image: none;
}

@media screen and (min-width: 48rem) {
  #photography .swiper {
    width: 100%;
  }
}

@media screen and (min-width: 93.75rem) {
  #photography .swiper {
    width: 85%;
  }
}
/* Slide background images using local assets */
#photography .swiper-slide--one {
  background: linear-gradient(
      to top,
      rgba(15, 32, 39, 0.8),
      rgba(32, 58, 67, 0.3),
      rgba(44, 83, 100, 0)
    ),
    url("./assets/photos/DSC03302.png") no-repeat 50% 50% / cover;
}

#photography .swiper-slide--two {
  background: linear-gradient(
      to top,
      rgba(15, 32, 39, 0.8),
      rgba(32, 58, 67, 0.3),
      rgba(44, 83, 100, 0)
    ),
    url("./assets/photos/DSC03471.jpg") no-repeat 50% 50% / cover;
}

#photography .swiper-slide--three {
  background: linear-gradient(
      to top,
      rgba(15, 32, 39, 0.8),
      rgba(32, 58, 67, 0.3),
      rgba(44, 83, 100, 0)
    ),
    url("./assets/photos/DSC03550.JPG") no-repeat 50% 50% / cover;
}

#photography .swiper-slide--four {
  background: linear-gradient(
      to top,
      rgba(15, 32, 39, 0.8),
      rgba(32, 58, 67, 0.3),
      rgba(44, 83, 100, 0)
    ),
    url("./assets/photos/DSC03763.JPG") no-repeat 50% 50% / cover;
}

#photography .swiper-slide--five {
  background: linear-gradient(
      to top,
      rgba(15, 32, 39, 0.8),
      rgba(32, 58, 67, 0.3),
      rgba(44, 83, 100, 0)
    ),
    url("./assets/photos/DSC03529.JPG") no-repeat 50% 50% / cover;
}

/* Add styles for project cards with videos */
.project-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: transform 0.3s ease, border-color 0.3s ease;
  overflow: hidden;
}

.project-card:hover {
  transform: translateY(-5px);
  border-color: rgba(56, 189, 248, 0.7);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.video-container {
  position: relative;
  overflow: hidden;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.video-container video {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .video-container video {
  transform: scale(1.05);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  padding: 0.75rem 1rem;
  font-family: "Orbitron", sans-serif;
  font-size: 1.125rem;
  color: white;
  transition: background-color 0.3s ease;
}

.project-card:hover .video-title {
  background: rgba(14, 165, 233, 0.7);
}

/* Ensure the portfolio grid has proper spacing and layout */
#portfolio-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  #portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  #portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Add styles for project links */
.project-card a {
  color: rgba(148, 163, 184, 1);
  transition: color 0.2s ease;
}

.project-card a:hover {
  color: rgba(56, 189, 248, 1);
}

/* Add styles for the experience timeline */
.timeline-line {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(56, 189, 248, 0.5),
    rgba(56, 189, 248, 0.8),
    rgba(56, 189, 248, 0.5),
    transparent
  );
  transform: translateX(-50%);
  z-index: 1; /* Lower z-index */
}

.timeline-item {
  position: relative;
  width: 45%;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  z-index: 2; /* Higher than the line but lower than the dock */
}

.timeline-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.timeline-item.left {
  margin-right: auto;
}

.timeline-item.right {
  margin-left: auto;
}

.timeline-item::before {
  content: "";
  position: absolute;
  top: 20px;
  width: 20px;
  height: 20px;
  background: rgba(56, 189, 248, 0.8);
  border-radius: 50%;
  z-index: 1;
}

.timeline-item.left::before {
  right: -60px;
}

.timeline-item.right::before {
  left: -60px;
}

.timeline-item::after {
  content: "";
  position: absolute;
  top: 30px;
  width: 40px;
  height: 2px;
  background: rgba(56, 189, 248, 0.5);
}

.timeline-item.left::after {
  right: -40px;
}

.timeline-item.right::after {
  left: -40px;
}

/* Responsive timeline */
@media (max-width: 768px) {
  .timeline-line {
    left: 20px;
  }

  .timeline-item {
    width: calc(100% - 60px);
    margin-left: 60px;
    margin-right: 0;
  }

  .timeline-item.left::before,
  .timeline-item.right::before {
    left: -50px;
    right: auto;
  }

  .timeline-item.left::after,
  .timeline-item.right::after {
    left: -30px;
    right: auto;
  }
}

/* Blog styles for single featured blog */
#blog-grid .glass-pane {
  transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

#blog-grid .glass-pane:hover {
  transform: translateY(-5px);
  border-color: rgba(56, 189, 248, 0.7);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

#blog-grid img {
  transition: transform 0.5s ease;
}

#blog-grid .glass-pane:hover img {
  transform: scale(1.05);
}

/* Photography section button */
#photography a.inline-block {
  transition: all 0.3s ease;
}

#photography a.inline-block:hover {
  box-shadow: 0 0 20px rgba(56, 189, 248, 0.4);
}

/* Animation for reveal sections */
.reveal-section {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-section.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure glass-pane elements don't overlap the dock */
.glass-pane {
  position: relative;
  z-index: 5; /* Higher than basic elements but lower than the dock */
}

/* Toast notification styles */
#greeting-toast {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.2);
  animation: none; /* Will be set via JS */
}

@keyframes slideInToast {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToast {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

#greeting-toast.show {
  animation: slideInToast 0.5s forwards ease-out;
}

#greeting-toast.hide {
  animation: slideOutToast 0.5s forwards ease-in;
}

/* Add a subtle pulse effect to the visitor number */
#visitor-toast-number span {
  display: inline-block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
    color: rgb(56, 189, 248);
  }
  100% {
    transform: scale(1);
  }
}

/* Light Theme Specific Styles */
.light-theme .glass-pane {
  background: var(--glass-bg);
  border: 1px solid var(--border-subtle);
  box-shadow: 0 4px 12px var(--card-shadow);
}

.light-theme .glass-pane:hover {
  border-color: var(--primary-border);
  box-shadow: 0 8px 16px rgba(14, 165, 233, 0.15);
  background: var(--highlight);
}

.light-theme #bottom-dock {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1),
              0 0 15px rgba(14, 165, 233, 0.2);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.light-theme .dock-item {
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.light-theme .dock-item:hover {
  color: var(--text-accent);
  background: var(--highlight);
  transform: translateY(-3px);
}

.light-theme #bottom-dock::before {
  background: radial-gradient(ellipse at center, rgba(14, 165, 233, 0.2) 0%, rgba(14, 165, 233, 0) 70%);
}

.light-theme #mobile-dock-toggle {
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-subtle);
}

.light-theme #mobile-dock-toggle:hover {
  color: var(--text-accent);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
}

.light-theme #mobile-dock-menu .dock-item {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--border-subtle);
}
