name: Deploy Backend Lambda Function

on:
  push:
    branches:
      - main
    paths:
      - 'lambda/**'

jobs:
  deploy:
    name: Deploy to AWS Lambda
    runs-on: ubuntu-latest

    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Zip Lambda function
        run: |
          cd lambda
          zip -r ../lambda.zip .

      - name: Deploy using AWS CLI
        run: |
          aws lambda update-function-code \
            --function-name viewCounterFunction \
            --zip-file fileb://lambda.zip \
            --region $AWS_REGION
