{"meta": {"title": "Puneeth Y | Cloud Resume", "description": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio. Explore my journey, skills, projects, and achievements.", "keywords": "Puneeth Y, Cloud Resume, DevOps, AWS, Kubernetes, Portfolio, Open Source, Engineer, Blog, Photography", "author": "Puneeth Y", "ogTitle": "Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast", "ogDescription": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio.", "twitterTitle": "Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast", "twitterDescription": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio."}, "navigation": {"home": "Home", "about": "About", "recommendations": "Recommendations", "pipeline": "Pipeline", "skills": "Skills", "projects": "Projects", "experience": "Experience", "blogs": "Blogs", "photography": "Photography"}, "hero": {"name": "Puneeth Y", "subtitle": "An Aspiring DevOps Engineer & Cloud Architect crafting resilient, automated, and scalable infrastructure for tomorrow's technology."}, "about": {"title": "About Me", "intro": "<PERSON><PERSON>, I'm an aspiring DevOps engineer with a passion for automation and system orchestration.", "description": "I love building efficient and reliable infrastructure using tools like Docker, Kubernetes, and AWS. Whether it's writing Infrastructure as Code, managing containers, or setting up CI/CD pipelines, I'm all about creating systems that make life easier for developers, helping them ship faster, safer, and with confidence.", "hobby": "Outside of tech, I enjoy photography and often spend time exploring nature with my camera, capturing moments when I can."}, "recommendations": {"title": "Recommendations", "description": "What colleagues and mentors have to say about working with me.", "rating": "5/5 Rating", "recommender1Name": "<PERSON><PERSON><PERSON>", "recommender1Position": "Technical Project Manager at HCLSoftware", "recommendation1": "I worked with <PERSON><PERSON> to create the migration plan for moving our projects to a new Packaged Business Capabilities (PBC) architecture. During this time, he quickly developed a deep understanding of the projects and their architectural requirements. His knowledge of Docker and Kubernetes was truly commendable and worth highlighting. Even as a fresher, he took ownership of PoC setups, thoroughly evaluating various Kubernetes deployment architectures and giving valuable feedback on the best approach. While I brought strong client domain expertise as the more experienced member, <PERSON><PERSON>’s prior exposure to modern cloud-native tools made us a powerful two-person migration team. This combination of strengths was key to our success. I truly enjoyed working with him and am confident he will be a valuable asset to your organization. I wholeheartedly recommend him and wish him the very best in his future endeavors.", "recommender2Name": "Dr. <PERSON><PERSON><PERSON>", "recommender2Position": "Head of Department at Adichunchanagiri Institute of Technology", "recommendation2": "I had the pleasure of supervising <PERSON><PERSON> during his studies at Adichunchanagiri Institute of Technology, and I can confidently say he is among the best students I have worked with. He has consistently demonstrated exceptional technical skills, academic excellence, and the ability to translate sophisticated concepts into impactful projects. From developing a highly acclaimed sentiment analyzer for Reddit posts in his third year to leading an ambitious final-year project on automated test suite generation in CI/CD pipelines using generative AI, <PERSON><PERSON> has shown both innovation and practical problem-solving ability. His dedication to producing high-quality, practical solutions, coupled with his curiosity and commitment to professional growth, makes him an outstanding candidate for any organization seeking a talented and forward-thinking engineer.", "recommender3Name": "Dr. <PERSON><PERSON>", "recommender3Position": "Professor of Computer Science at Tech University", "recommendation3": "<PERSON><PERSON> was one of my most dedicated students, showing remarkable aptitude for system design and automation. His final project on container orchestration was outstanding and demonstrated his ability to tackle complex technical challenges with creativity and precision.", "recommender4Name": "<PERSON>", "recommender4Position": "Team Lead at StartupTech", "recommendation4": "<PERSON><PERSON>'s contribution to our open-source projects during GSSoC was exceptional. His code quality, attention to detail, and collaborative spirit made him stand out among thousands of contributors. He consistently delivered beyond expectations and helped mentor other contributors."}, "stats": {"experience": "Years of Experience", "visitors": "Visitors", "poweredBy": "Powered by AWS Lambda", "commitment": "Commitment to Automation"}, "skills": {"title": "My skills", "description": "I have strong hands-on experience with DevOps tools and practices, particularly in deploying and managing cloud-native applications on AWS. My skill set includes working with Kubernetes, Helm, and Terraform to automate deployments, as well as using Prometheus and Grafana for monitoring and observability. I also bring experience in building CI/CD pipelines, integrating automated testing, and ensuring smooth delivery workflows. On the development side, I’m comfortable with languages like Python, Golang, JavaScript, and frameworks such as Node.js, Django, and .NET, which helps me bridge the gap between development and operations. I’ve worked with both MSSQL and MongoDB for data-driven applications, making me well-rounded in managing full-stack systems from code to production.  Curious for more? Feel free to check out my CV along the way!", "infrastructure": "On the development side, I’m comfortable with languages like Python, Golang, JavaScript, and frameworks such as Node.js, Django, and .NET, which helps me bridge the gap between development and operations. I’ve worked with both MSSQL and MongoDB for data-driven applications, making me well-rounded in managing full-stack systems from code to production.", "curious": "Curious for more? Feel free to check out my CV along the way!", "viewCV": "View CV"}, "pipeline": {"code": "Code", "build": "Build", "test": "Test", "deploy": "Deploy"}, "portfolio": {"title": "Featured Projects", "description": "A selection of my recent software and DevOps projects. Hover over each card to see more details.", "portfolioProject0title": "CODESOURCERER", "portfolioProject1title": "Vitista", "portfolioProject2title": "Sputilties", "portfolioProject3title": "<PERSON><PERSON>", "portfolioProject0description": "A tool that automates test suite generation for code changes using a Gemini-powered proxy server, completely integrates with GitHub to create filtered tests via pull requests.", "portfolioProject1description": "A comprehensive personal healthcare app designed to empower individuals on their journey to optimal well-being.", "portfolioProject2description": "A collection of Spotify utilities and tools to enhance your music streaming experience.", "portfolioProject3description": "A collaborative meeting and team communication platform for remote teams."}, "certifications": {"title": "Awards & Certifications", "description": "A collection of certifications and recognitions I've received along my learning and DevOps journey.", "items0": "Top 1% in GSSoC'ext 2024", "items1": "1st Place in the department-level mini-project", "items2": "AZ-305 Microsoft Azure Architect Design Prerequisites", "items3": "AWS Cloud Practitioner Essentials", "items4": "Postman API Fundamentals Student Expert"}, "experience": {"title": "Work Experience", "description": "My professional journey in the world of DevOps and cloud engineering.", "jobs0Company": "HCLSoftware", "jobs0position": "Intern", "jobs0period": "Mar 2025 - Aug 2025", "jobs0description": "Played a key role in transitioning a client’s monolithic application to a modular, Packaged Business Capabilities (PBC) oriented architecture, resulting in a significant improvement in deployment speed and system scalability; leveraged Docker and Multi-node K8s within Agile workflows to streamline delivery and enhance maintainability in serveral initial proof of concept (PoC) applications; implemented a complete observability stack including monitoring, logging, and distributed tracing to ensure end-to-end system visibility and maintainability; Was one of the first members of the two-person migration team tasked with designing the migration plan; contributed to the initial PoC validating the feasibility of implementing the client project in the new PBCoriented architecture, and successfully migrated it while upgrading the tech stack along the way.", "jobs1Company": "GirlScript Summer of Code (GSSoC-ext'24)", "jobs1position": "Open Source Contributor", "jobs1period": "Oct 2024 - Nov 2024", "jobs1description": "Actively contributed to open-source repositories as part of GSSoC-ext 2024, demonstrating strong coding proficiency, collaboration, and problem-solving abilities. Achieved a top ranking of 281 out of 60,000 participants, reflecting high technical competence and consistent engagement throughout the program."}, "blog": {"title": "My Blog", "description": "Sharing my journey and insights in cloud computing and DevOps.", "readBlog": "Read The Blog", "posts0title": "My Attempt at the AWS Cloud Resume Challenge", "posts0excerpt": "Curious about how the architecture behind this website is structured using modern DevOps practices? I break it all down in my blog post as part of the Cloud Resume Challenge.\n\nFrom CI/CD to infrastructure as code, it's all in there. Be sure to check it out!"}, "photography": {"title": "Photography Portfolio", "description": "Beyond the code, I enjoy capturing the world through my lens. Here's a glimpse of my creative side.", "viewFull": "View Full Photography Portfolio"}, "footer": {"builtWith": "Built as part of the AWS Cloud Resume Challenge.", "copyright": "© 2025 Puneeth Y. All Rights Reserved."}, "toast": {"welcome": "Welcome!", "thanks": "Thanks for visiting my portfolio.", "visitor": "You are visitor #"}, "ui": {"close": "×"}}