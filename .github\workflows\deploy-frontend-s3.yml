name: Deploy Static Frontend to S3

on:
  push:
    branches:
      - main
    paths:
      - 'frontend/**'

jobs:
  deploy:
    name: Upload to S3
    runs-on: ubuntu-latest

    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      S3_BUCKET_NAME: pyd773-frontend-bucket

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Sync HTML/CSS files to S3
        run: |
          aws s3 sync frontend/ s3://$S3_BUCKET_NAME \
            --delete \
            --acl public-read \
            --region $AWS_REGION
