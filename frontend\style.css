@import url('https://fonts.googleapis.com/css?family=Oswald|Rambla|Staatliches&display=swap');

body{
    margin: 0;
    padding: 0;
}

:root{
    --color-black: #000000;
    --color-white: #ffffff;
    --color-border: #ffffff34;
    --color-blue: #000000;
    --font-staat: 'Staatliches', cursive;
    --font-os:  '<PERSON>', sans-serif;
    --font-ram: 'Rambla', sans-serif;

    /* Theme variables for light and dark mode */
    --theme-bg: #fff;
    --theme-text: #222;
    --theme-card: #f8f9fa;
    --theme-nav: #000;
}

/* Dark mode variables */
[data-theme="dark"] {
    --theme-bg: #181818;
    --theme-text: #fff;
    --theme-card: #232323;
    --theme-nav: #181818;
}

/* global classes */
.font-staat{
    font: normal 400 18px var(--font-staat);
}
.font-os{
    font: normal 300 18px var(--font-os);
}
.font-ram{
    font: normal bold 18px var(--font-ram);
}

.font-size-40{
    font-size: 40px;
}
.font-size-34{
    font-size: 34px;
}
.font-size-27{
    font-size: 27px;
}
.font-size-20{
    font-size: 20px;
}
.font-size-16{
    font-size: 16px;
}

.bgcolor-black{
    background-color: var(--color-blue);
}

/* #global classes */

#header{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index:1;
    transition: left .5s ease;
}

#header nav{
    height: 100vh;
}
#header .site-title .navbar-brand{
    letter-spacing: 2px;
    color: var(--color-white);
}

#header .nav-link{
    margin: .7rem 1rem;
    border-bottom: 1px solid var(--color-border);
    text-transform: uppercase;
}

#header .nav-link:hover{
    color: var(--color-white) !important;
}

#header .toggle-button{
    background: none;
    color: var(--color-black);
    position: fixed;
    top: 25px;
    right: 20px;
    border: 1px solid var(--color-border);
}

.toggle-left{
    left: 0 !important;
    width: 1000px !important;
}


.nav-link {
    font-weight: 500;
    padding: 12px 20px !important;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    opacity: 1;
    transform: translateX(5px);
}

.nav-link.active {
    opacity: 1;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%;
    background-color: var(--color-accent);
    border-radius: 0 4px 4px 0;
}

/* site-main */

.site-banner .banner-area{

    background-size: cover;
    width: 100%;
    height: 100vh;
    position: relative;
}

.site-banner .banner-area .author{
    margin: 0;
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.site-banner .banner-area .author .author-img{
    width: 240px;
    height: 240px;
    border-radius: 50%;
    margin: auto;
    background: url(./assets/avatar-alt1.png) no-repeat;
    background-size: 110%;
    background-position: 40% 0;
}

.skill .bars p, .site-content .skill .bars span{
    line-height: 10px;
}

#footer form .row .col input[type="text"],
#footer form .row .col input[type="email"],
#footer form textarea{
    border: none;
    border-radius: 0;
    border-bottom: 1px solid gray;
    padding: 1.5rem 1rem;
}

@media screen and (min-width: 768px){
    .toggle-button{
        display: none;
    }
    #header{
        z-index:0;
    }
}

#typed, #typed_2 {
    color: #92d805;
    font-weight: 600;
    display: inline-block;
}

.typed-cursor {
    opacity: 1;
    animation: blink 0.7s infinite;
    font-size: 1.1em;
    vertical-align: middle;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}

.typed-container {
    min-height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}
/* #site-main */


/*icons*/
.social-icons a{
    display:inline-block;height:3.5rem;
    width:3.5rem;
    background-color: #fff;
    color:#92d805!important;
    border-radius:100%;
    text-align:center;
    font-size:1.5rem;
    line-height:3.5rem;
    margin-right:1rem
  }
  
  .social-icons a:last-child{
    margin-right:0
  }
  
  .social-icons a:hover{
    background-color:var(--color-blue);
    transform: scale(1.2)
  }
  
  .social-icons a:hover i {
    transform: scale(1.3);
  }

  .dev-icons {
    font-size: 5rem;
  }
  
  .dev-icons .list-inline-item :hover {
    color: var(--color-blue);
  }
  /*icons*/



  /*project*/
.frame {
	text-align: center;	
	position: relative;
	cursor: pointer;	
	perspective: 500px; 
}

.frame .details {
	width: 90%;
	height: 90%;	
	padding: 5% 8%;
	position: absolute;
	content: "";
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotateY(90deg);
	transform-origin: 50%;
	background: var(--color-blue);	
	opacity: 0;
    transition: all 0.4s ease-in;
    color: var(--color-white);
    font-family: var(--font-ram);
}

.frame:hover .details {
	transform: translate(-50%, -50%) rotateY(0deg);
	opacity: 1;
}


/*project icons- github and live site*/
.giti, .eye{
    font-size: 3rem;
    color: var(--color-white);
}

.eye :hover{
    color: var(--color-white);
}

/* Work section container */
.work {
    display: flex;
    flex-wrap: wrap; /* Allow items to wrap to the next line */
    gap: 1.5rem; /* Add spacing between items */
    justify-content: center; /* Center items horizontally */
    align-items: flex-start; /* Align items to the top */
    padding: 2rem 1rem; /* Add padding around the section */
}

/* Standardize work card dimensions */
.work-card {
    flex: 1 1 320px;
    max-width: 420px;
    min-width: 260px;
    width: 100%;
    height: 300px;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
                box-shadow 0.4s ease,
                opacity 0.4s ease;
    margin: 0 auto;
    opacity: 1;
    transform-origin: center;
}

/* Enhanced hover effect for work card */
.work-card:hover {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

/* Work card entrance animation */
@keyframes cardEntrance {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Apply entrance animation to all work cards */
.work-card {
    animation: cardEntrance 0.6s cubic-bezier(0.21, 1.02, 0.73, 1) forwards;
}

/* Stagger animation for multiple cards */
.col-12:nth-child(2) .work-card {
    animation-delay: 0.1s;
}
.col-12:nth-child(3) .work-card {
    animation-delay: 0.2s;
}
.col-12:nth-child(4) .work-card {
    animation-delay: 0.3s;
}

/* Video inside work card */
.work-card video {
    width: 100%; /* Make video responsive */
    height: 100%; /* Ensure video fills the card */
    object-fit: cover; /* Ensure video covers the container */
    border-radius: inherit; /* Inherit rounded corners */
}

/* Improved overlay animation */
.work-card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    backdrop-filter: blur(2px);
}

.work-card:hover .overlay {
    opacity: 1;
}

/* Enhanced icon animations */
.work-card .overlay a {
    color: #fff;
    font-size: 2rem;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.7rem;
    border-radius: 50%;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                background 0.3s ease,
                color 0.3s ease;
    transform: translateY(20px);
    opacity: 0;
}

.work-card:hover .overlay a {
    transform: translateY(0);
    opacity: 1;
}

.work-card:hover .overlay a:nth-child(1) {
    transition-delay: 0.1s;
}

.work-card:hover .overlay a:nth-child(2) {
    transition-delay: 0.2s;
}

.work-card .overlay a:hover {
    transform: scale(1.2);
    background: rgba(255, 255, 255, 0.2);
    color: #ffd700;
}

/* Details section */
.work-card .details {
    text-align: center;
    padding: 1rem;
    background: #fff;
    font-family: var(--font-ram);
    font-size: 1.2rem;
    color: #333;
}

/* Video title overlay for each work card */
.work-card .video-title {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.55);
    color: #fff;
    font-family: var(--font-staat);
    font-size: 1.2rem;
    text-align: center;
    padding: 0.5rem 0.2rem 0.4rem 0.2rem;
    z-index: 2;
    letter-spacing: 1px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    pointer-events: none; /* So overlay icons remain clickable */
}

  .carousel-inner img {
    transition: transform 0.5s ease;
  }

  .carousel-inner img:hover {
    transform: scale(1.03);
  }

  /* Custom carousel buttons */
  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
  }

  .carousel-control-prev-icon::before {
    content: "\f104"; /* Font Awesome left chevron */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: white;
  }

  .carousel-control-next-icon::before {
    content: "\f105"; /* Font Awesome right chevron */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: white;
  }
  
/* Responsive adjustments */
@media screen and (max-width: 1200px) {
    .work-card {
        flex: 1 1 45%;
        max-width: 95vw;
        min-width: 220px;
        height: 240px;
    }
}

@media screen and (max-width: 768px) {
    .work-card {
        flex: 1 1 90%;
        max-width: 98vw;
        min-width: 180px;
        height: 180px;
    }
}

@media screen and (max-width: 480px) {
    .work-card {
        flex: 1 1 100%;
        max-width: 100vw;
        min-width: 140px;
        height: 140px;
    }
}

/* Responsive banner adjustments */
.site-banner .banner-area {
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.author-img {
    width: 260px;
    height: 260px;
    border-radius: 50%;
    margin: 0 auto 1rem auto;
    background: url(./assets/avatar-alt1.png) no-repeat center center;
    background-size: cover;
}

.typed-container {
    min-height: 38px; /* Fixed height to prevent layout shifts */
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .site-banner .banner-area {
        min-height: 40vh;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    .author-img {
        width: 260px;
        height: 260px;
    }
    .font-size-40 {
        font-size: 2rem;
    }
    .font-size-27 {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .site-banner .banner-area {
        min-height: 30vh;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
    .author-img {
        width: 260px;
        height: 260px;
    }
    .font-size-40 {
        font-size: 2rem;
    }
    .font-size-27 {
        font-size: 1rem;
    }
}

#photographyCarousel {
    cursor: default !important;
    user-select: none;
}

/* Consistent gray button styles */
.btn-primary {
    background: linear-gradient(90deg, #444 0%, #222 100%);
    border: none;
    color: #fff !important;
    box-shadow: 0 4px 12px rgba(52, 219, 66, 0.2);
    border-radius: 30px;
    transition: all 0.3s ease;    
    letter-spacing: 1px;
    padding: 10px 24px;
}
.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(52, 219, 91, 0.3);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid #444;
    color: #444 !important;
    border-radius: 30px;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    letter-spacing: 1px;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background: #444;
    color: #ffd700 !important;
    transform: translateY(-2px) scale(1.04);
    outline: none;
}

.btn, button {
    font-family: var(--font-ram);
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    box-shadow: none;
}

/* Experience Timeline Styles */
.experience .timeline {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    padding-left: 30px;
    border-left: 3px solid #444;
}

.timeline-item {
    position: relative;
    margin-bottom: 2.5rem;
}

.timeline-dot {
    position: absolute;
    left: -12px;
    top: 0.7em;
    width: 18px;
    height: 18px;
    background: #444;
    border-radius: 50%;
    border: 3px solid #fff;
    z-index: 1;
    box-shadow: 0 0 0 2px #444;
}

.timeline-details summary {
    cursor: pointer;
    font-size: 1.2rem;
    font-family: var(--font-ram);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: none;
    color: #222;
    padding: 0.5rem 0;
    outline: none;
    border-radius: 6px;
    transition: background 0.2s;
}

.timeline-details[open] summary {
    background: #f3f3f3;
}

[data-theme="dark"] .timeline-details[open] summary {
    background: #232323;
    color: #fff;
}

.timeline-details ul {
    margin: 0.5rem 0 0 0.5rem;
    padding-left: 1.2rem;
    color: #444;
}

.timeline-details[open] ul {
    animation: fadeIn 0.3s;
}

.timeline-details .duration {
    font-size: 1rem;
    color: #888;
    margin-left: 1rem;
}

.timeline-details .company {
    font-weight: bold;
    color: #222;
}

[data-theme="dark"] .timeline-details .company {
    color: #fff;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px);}
    to { opacity: 1; transform: translateY(0);}
}

#greeting-toast {
    position: fixed;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Experience Section Styles */
.exp-details {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    padding: 1rem 1.5rem;
    transition: box-shadow 0.2s;
    border: none;
}
.exp-details[open] {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.exp-summary {
    cursor: pointer;
    font-size: 1.1rem;
    font-family: var(--font-ram);
    background: none;
    color: #222;
    padding: 0.2rem 0;
    outline: none;
    border-radius: 6px;
    transition: background 0.2s;
}
.exp-details[open] .exp-summary {
    background: transparent;
}
.exp-details ul {
    margin: 0.5rem 0 0 0.5rem;
    padding-left: 1.2rem;
    color: #444;
    font-size: 1rem;
}
.exp-details .duration {
    font-size: 0.98rem;
    color: #888;
    margin-top: 0.5rem;
}
.exp-details .company {
    font-weight: bold;
    color: #222;
}
@media (max-width: 768px) {
    .exp-details {
        padding: 1rem 0.7rem;
    }
    .exp-summary {
        font-size: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.2rem;
    }
}
@media (max-width: 480px) {
    .exp-details {
        padding: 0.7rem 0.3rem;
    }
    .exp-summary {
        font-size: 0.98rem;
    }
}

/* Vertical timeline line for experience section */
.exp-timeline-container {
    position: relative;
    max-width: 700px;
    margin: 0 auto;
    padding-left: 32px; /* space for the line */
}

.exp-timeline-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 12px;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #444 0%, #bbb 100%);
    border-radius: 2px;
    z-index: 0;
}

/* Link each box to the line with a dot */
.exp-details {
    position: relative;
    margin-bottom: 2.5rem;
    margin-left: 0;
    margin-right: 0;
    z-index: 1;
}

.exp-details::before {
    content: "";
    position: absolute;
    left: -32px;
    top: 1.3rem;
    width: 18px;
    height: 18px;
    background: #fff;
    border: 4px solid #444;
    border-radius: 50%;
    z-index: 2;
    box-shadow: 0 0 0 2px #bbb;
    display: block; /* Always show the dot */
}

/* Experience Section Animation Improvements */
.exp-details[open] ul {
    animation: fadeIn 0.4s ease-out;
}

.exp-details ul {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.exp-details:not([open]) ul {
    animation: fadeOut 0.3s ease-out forwards;
}

.exp-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding-right: 30px; /* Space for the arrow */
}

.exp-summary::after {
    content: '\f107'; /* Font Awesome down arrow */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.exp-details[open] .exp-summary::after {
    transform: translateY(-50%) rotate(180deg);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* Fix for experience timeline layout */
.exp-summary .company {
    flex: 1;
    text-align: left;
}

.exp-summary .duration {
    flex: 0 0 auto;
    text-align: right;
    margin-left: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .exp-summary {
        flex-direction: column;
        align-items: flex-start;
        padding-right: 30px;
    }
    
    .exp-summary .duration {
        margin-left: 0;
        margin-top: 5px;
    }
    
    .exp-summary::after {
        top: 15px;
        transform: none;
    }
    
    .exp-details[open] .exp-summary::after {
        transform: rotate(180deg);
    }
}

/* Fix sidebar overlap on mobile */
@media (max-width: 768px) {
  #header.toggle-left {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: transparent !important; /* Let sidebar shape show naturally */
    width: auto !important; /* Don't force full width */
  }
  #header .row {
    margin: 0 !important;
  }
  #header .col-3 {
    min-width: 260px !important;
    max-width: 320px !important;
    width: 85vw !important; /* Responsive but not full screen */
    padding: 0 !important;
    background: #000 !important; /* Sidebar stays solid */
    height: 100vh !important;
  }
}
