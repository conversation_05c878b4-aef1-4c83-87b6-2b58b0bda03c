<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puneeth Y | Cloud Resume</title>
    <meta name="description" content="Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio. Explore my journey, skills, projects, and achievements.">
    <meta name="keywords" content="Puneeth Y, Cloud Resume, DevOps, AWS, Kubernetes, Portfolio, Open Source, Engineer, Blog, Photography">
    <meta name="author" content="Puneeth Y">
    <link rel="canonical" href="https://puneeth.is-a.dev/" />
    <link rel="icon" type="image/png" href="./assets/avatar-alt1.png" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast">
    <meta property="og:description" content="Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio.">
    <meta property="og:image" content="https://puneeth.is-a.dev/assets/avatar-alt1.png">
    <meta property="og:url" content="https://puneeth.is-a.dev/">
    <meta property="og:type" content="website">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast">
    <meta name="twitter:description" content="Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio.">
    <meta name="twitter:image" content="https://puneeth.is-a.dev/assets/avatar-alt1.png">

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Puneeth Y",
      "url": "https://puneeth.is-a.dev/",
      "image": "https://puneeth.is-a.dev/assets/avatar-alt1.png",
      "sameAs": [
        "https://www.linkedin.com/in/puneeth072003/",
        "https://github.com/puneeth072003",
        "https://twitter.com/puneeth072003"
      ],
      "jobTitle": "DevOps Engineer",
      "worksFor": {
        "@type": "Organization",
        "name": "HCLSoftware"
      }
    }
    </script>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css"
        integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">

    <link rel="stylesheet" href="./style.css">
    <link rel="icon" type="image/png" href="./assets/avatar-alt1.png" />

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/themes/light.css" />

</head>

<body>

    <!-- header area -->
    <header id="header">
        <div class="row m-0">
            <div class="col-3 bgcolor-black">
                <nav class="primary-nav navbar-expand-md">
                    <div class="site-title text-center text-light py-5">
                        <a href="#" class="navbar-brand font-staat font-size-40">PY</a>
                    </div>
                    <div class="d-flex flex-column">
                        <a href="#home" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-home" style="margin-right: 8px;"></i> Home
                        </a>
                        <a href="#about_me" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-user" style="margin-right: 8px;"></i> About
                        </a>
                        <a href="#awards" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-trophy" style="margin-right: 8px;"></i> Achievements
                        </a>
                        <a href="#experience" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-briefcase" style="margin-right: 8px;"></i> Experience
                        </a>
                        <a href="#portfolio" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-laptop" style="margin-right: 8px;"></i> Portfolio
                        </a>
                        <a href="#blogs" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-pen-nib" style="margin-right: 8px;"></i> Blogs
                        </a>
                        <a href="#photography" class="nav-item nav-link text-white-50 font-os font-size-16 active">
                          <i class="fas fa-camera-retro" style="margin-right: 8px;"></i> Photography
                        </a>
                        <!-- Replace the sl-details block with a styled dropdown matching sidebar links -->
                        <div class="nav-item nav-link text-white-50 font-os font-size-16 active p-0" style="border-bottom: 1px solid #ffffff34;">
                            <details style="width: 100%;">
                                <summary style="cursor: pointer; outline: none; padding: 0.7rem 1rem; background: transparent; color: inherit; font-family: inherit; font-size: inherit; text-transform: uppercase;">
                                    <i class="fas fa-eye" style="margin-right: 8px;"></i> Visitors
                                </summary>
                                <div style="background: #222; border-radius: 0 0 8px 8px; padding: 1rem 1rem 0.5rem 1rem;">
                                    <div class="counter-number" style="color: #fff; font-family: var(--font-os); font-size: 1rem; margin-bottom: 0.5rem;">
                                        Couldn't read the counter
                                    </div>
                                    <div class="font-os text-white-50" style="font-size: 0.95rem;">
                                        Part of the AWS Cloud Resume Challenge
                                    </div>
                                </div>
                            </details>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        <button class="toggle-button"><span class="fas fa-bars fa-2x"></span></button>
    </header>
    <!-- #header area -->

    <main id="site-main">
        <div class="row m-0">
            <div class="col-md-9 offset-md-3 px-0">

                <!-- site-banner area -->
                <section class="site-banner bg-light" id="home">
                    <div class="banner-area d-flex flex-column align-items-center justify-content-center text-center">
                        <div class="author-img mx-auto mb-3"></div>
                        <h1 class="text-black font-staat font-size-40 text-uppercase py-2 mb-1">Puneeth Y</h1>
                        <div class="typed-container mb-2">
                            <h5 class="text-black font-ram font-size-27 mb-0">I'm a <span id="typed"></span></h5>
                        </div>
                    </div>
                </section>
                <!-- #site-banner area -->

                <!-- about me area -->
                <section class="about px-4 my-5" id="about_me">
                    <div class="me py-5">
                        <h5 class="text-uppercase font-os font-size-16 text-muted">
                            Information
                        </h5>
                        <h1 class="text-uppercase font-staat font-size-34">About Me</h1>
                    </div>
                    <div class="row m-0">
                        <div class="col-sm-5 pl-0">
                            <img src="./assets/3301602.jpg" alt="profile image" class="img-fluid">
                        </div>
                        <div class="col-sm-6">
                            <h6 class="text-uppercase font-os font-size-16 text-muted">About Me</h6>
                            <h5 class="font-ram font-size-20 py-2">I'm Puneeth Y & a <span id="typed_2"></span></h5>
                            <p class="font-ram text-black-50 py-2">
                                I'm a final-year Computer Science student with a strong passion for automation and system orchestration. I enjoy working with technologies like Docker, Kubernetes, and AWS to build efficient and reliable infrastructure. One of my key strengths is designing smooth CI/CD pipelines that streamline development workflows. 
                                <br/><br/>
                                Outside of tech, I enjoy photography and often spend time exploring nature with my camera, capturing moments when I can. 
                            </p>
                            <div class="d-flex flex-row flex-wrap justify-content-between py-4">
                                <div class="d-flex flex-column">
                                    <!-- <p class="font-ram"><b>Website: </b><span
                                            class="text-black-50">puneeth.com</span></p> -->
                                </div>
                            </div>
                            <button type="button" onclick="window.open('./assets/Puneeth_Y.pdf')" 
                            class="btn btn-primary text-uppercase font-ram font-size-14 font-weight-normal px-4 py-2 mb-2">
                            Download CV
                            </button>
                        </div>
                    </div>
                </section>
                <!-- #about me area -->

                <!-- ability -->
                <section class="skill px-4 py-5 bg-light" id="services">
                    <div class="ability py-3">
                        <h5 class="text-uppercase font-os font-size-16 text-muted">
                            Ability
                        </h5>
                        <h1 class="text-uppercase font-staat font-size-34">My skills</h1>
                    </div>
                    <div class="row">
                        <div class="col-sm-8 pl-4">
                            <p class="font-ram font-size-16 text-black-50 pb-3">
                                I have hands-on experience with HTML, CSS, JavaScript, Python, and Golang. I'm also familiar with frameworks and libraries such as React, .NET, Django, and Node.js, often integrating them in full-stack development projects.
                                <br/><br/>
                                My infrastructure knowledge includes working with Kubernetes, Helm, Terraform, Prometheus, and Grafana to manage, deploy, and monitor applications efficiently.
                                <br/><br/>
                                On the data side, I’ve worked with both relational and non-relational databases, including MSSQL and MongoDB. I’ve also gained practical experience with AWS services, particularly for deploying and scaling cloud-native applications.
                                <br/><br/>
                                Curious for more? Feel free to check out my CV along the way!
                            </p>
                            <ul class="list-inline dev-icons">
                                <li class="list-inline-item">
                                    <i class="fab fa-html5"></i>
                                </li>
                                <!-- <li class="list-inline-item">
                                    <i class="fab fa-css3-alt"></i>
                                </li> -->
                                <li class="list-inline-item">
                                    <i class="fab fa-node-js"></i>
                                </li>
                                <li class="list-inline-item">
                                    <i class="fab fa-react"></i>
                                </li>
                                <li class="list-inline-item">
                                    <i class="fab fa-aws"></i>
                                </li>
                            </ul>
                        </div>
                </section>
                <!-- #ability -->


                <!-- awards -->
                <section class="awards px-4 py-5" id="awards">
                    <div class="do py-5">
                        <h5 class="text-uppercase font-os font-size-16 text-muted">
                            What I have achieved
                        </h5>
                        <h1 class="text-uppercase font-staat font-size-34"><i class="fas fa-award"></i> Awards &amp;
                            Certifications</h1>
                    </div>
                    <div class="w-100">

                        <ul class="fa-ul mb-0 font-ram font-size-16 text-black-50 pb-3">
                            <!-- <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                AWS Certified Developer Associate
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                AWS Certified Solutions Architect Associate
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                AWS Certified Cloud Practitioner
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                Oracle Cloud Infrastructure Foundations 2020 Certified Associate
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                Dean's List - Computer Networking & Technical Support
                            </li> -->
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                Top 1% in GSSoC'ext 2024.
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                1st Place in the department-level mini-project.
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                1st Place in the intra-college Hackathon.
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                AZ-305 Microsoft Azure Architect Design Prerequisites
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                AWS Cloud Practitioner Essentials.
                            </li>
                            <li>
                                <i class="fa-li fa fa-trophy text-warning"></i>
                                Postman API Fundamentals Student Expert.
                            </li>
                        </ul>
                    </div>
                </section>

                <!-- #services-->

                <!-- Experience Section -->
                <section class="experience px-4 py-5 bg-light" id="experience">
                  <div class="text-center mb-5">
                    <h5 class="text-uppercase font-os font-size-16 text-muted">Experience</h5>
                    <h1 class="text-uppercase font-staat font-size-34">My Journey</h1>
                    <hr style="width: 60px; border: 2px solid #444; margin: 0.5rem auto 1.5rem auto; border-radius: 2px;">
                  </div>
                  <div class="exp-timeline-container">
                    <!-- All .exp-details go here -->
                    <!-- Experience Item -->
                    <details class="exp-details mb-4">
                      <summary class="exp-summary">
                        <span class="company font-ram">Intern @ HCLSoftware</span>
                        <span class="duration font-os">March 2025 – Present</span>
                      </summary>
                      <ul class="font-ram mt-2 mb-1 pl-3">
                        <li>Played a key role in transitioning a monolithic application to a modular, Packaged Business Capabilities (PBC)-oriented architecture, which led to notable improvements in deployment speed, scalability, and system flexibility.</li>
                        <br>
                        <li>Contributed as a primary team member in architecting the upcoming cloud-native migration blueprint, focusing on defining the target architecture, technology stack, and deployment patterns tailored for future scalability and maintainability.</li>
                        <br>
                        <li>Led several targeted proofs of concept (PoCs) to validate new tools, frameworks, and deployment strategies, directly influencing final architecture decisions and shaping the release automation framework.</li>
                      </ul>
                      <div class="font-os text-muted" style="font-size: 0.95rem;">6 months</div>
                    </details>
                    <!-- Experience Item -->
                    <details class="exp-details mb-4">
                      <summary class="exp-summary">
                        <span class="company font-ram">Contributor @ GSSoC-ext'24</span>
                        <span class="duration font-os">Oct 2024 – Nov 2024</span>
                      </summary>
                      <ul class="font-ram mt-2 mb-1 pl-3">
                        <li>Actively contributed to open-source repositories as part of GSSoC-ext 2024, demonstrating strong coding proficiency, collaboration, and problem-solving abilities.</li>
                        <br>
                        <li>Achieved a top ranking of 281 out of 60,000 participants, reflecting high technical competence and consistent engagement throughout the program.</li>
                      </ul>
                      <div class="font-os text-muted" style="font-size: 0.95rem;">1 month</div>
                    </details>
                    <!-- Add more experience items as needed -->
                  </div>
                </section>
                <!-- #experience -->

                <!-- work -->
                <section class="work py-5 px-4 bg-white" id="portfolio">
                    <div class="text-center py-3">
                      <h5 class="text-uppercase font-os font-size-16 text-muted">
                            Portfolio
                      </h5>
                      <h1 class="text-uppercase font-staat font-size-34">Creative Work</h1>
                    </div>
                  
                    <div class="row">
                      <!-- 2 per row on small+ -->
                      <div class="col-12 col-sm-6 pb-4">
                        <div class="work-card">
                          <video src="./videos/CS-demo.mp4" autoplay loop muted></video>
                          <div class="video-title">CODESOURCERER</div>
                          <div class="overlay">
                            <a href="https://codesourcerer.webflow.io" title="Live Site">
                              <i class="far fa-eye eye"></i>
                            </a>
                            <a href="https://github.com/puneeth072003/CODESOURCERER" title="Source Code">
                              <i class="fab fa-github-square giti"></i>
                            </a>
                          </div>
                        </div>
                      </div>
                  
                      <div class="col-12 col-sm-6 pb-4">
                        <div class="work-card">
                          <video src="./videos/vitista.mp4" autoplay loop muted></video>
                          <div class="video-title">Vitista</div>
                          <div class="overlay">
                            <a href="https://vitista.vercel.app/" title="Live Site">
                              <i class="far fa-eye eye"></i>
                            </a>
                            <a href="https://github.com/puneeth072003/Vitista" title="Source Code">
                              <i class="fab fa-github-square giti"></i>
                            </a>
                          </div>
                        </div>
                      </div>
                  
                      <div class="col-12 col-sm-6 pb-4">
                        <div class="work-card">
                          <video src="./videos/Sputilties-demo.mp4" autoplay loop muted></video>
                          <div class="video-title">Sputilties</div>
                          <div class="overlay">
                            <a href="https://sputilities.netlify.app/" title="Live Site">
                              <i class="far fa-eye eye"></i>
                            </a>
                            <a href="https://github.com/puneeth072003/sputilities.V1" title="Source Code">
                              <i class="fab fa-github-square giti"></i>
                            </a>
                          </div>
                        </div>
                      </div>
                  
                      <div class="col-12 col-sm-6 pb-4">
                        <div class="work-card">
                          <video src="./videos/huddle.mp4" autoplay loop muted></video>
                          <div class="video-title">Huddle</div>
                          <div class="overlay">
                            <a href="https://ho-huddle.vercel.app/" title="Live Site">
                              <i class="far fa-eye eye"></i>
                            </a>
                            <a href="https://github.com/puneeth072003/huddle" title="Source Code">
                              <i class="fab fa-github-square giti"></i>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </section>
                <!-- #work -->

                <!-- Blogs Section -->
                <section class="blogs px-4 py-5 bg-light" id="blogs">
                    <div class="text-center mb-4">
                        <h5 class="text-uppercase font-os font-size-16 text-muted">
                            Explore
                        </h5>
                        <h1 class="text-uppercase font-staat font-size-34 mb-3">Latest Writings</h1>
                        <p class="font-ram font-size-16 text-black-50 mt-2 mb-0">
                            Thoughts on DevOps, Cloud, and the everyday tech hustle.
                        </p>
                    </div>
                    <div class="d-flex justify-content-center">
                        <div class="blog-card bg-light shadow rounded-3 p-4 p-md-5 text-center mx-auto" style="max-width:600px;">
                            <!-- <h2 class="font-staat mb-3">Read My Blogs</h2> -->
                            <figure class="mb-4">
                                <img
                                  src="./assets/Cover.png"
                                  alt="Cover image of blog"
                                  class="img-fluid rounded"
                                  style="max-width:100%;height:auto;"
                                  onclick="window.open('https://dev.to/puneeth072003/my-attempt-at-the-aws-cloud-resume-challenge-a-journey-in-the-cloud-13gd', '_blank')"
                                  title="Click to read the blog"
                                />
                            </figure>
                            <p class="font-ram font-size-16 text-black-50 pb-3 mb-4">
                                Curious about how the architecture behind this website is structured using modern DevOps practices? I break it all down in my blog post as part of the <strong>Cloud Resume Challenge</strong>. 
                                <br/><br/>
                                From CI/CD to infrastructure as code, it’s all in there. Be sure to check it out!
                            </p>
                            <a href="https://dev.to/puneeth072003/my-attempt-at-the-aws-cloud-resume-challenge-a-journey-in-the-cloud-13gd" target="_blank"
                               class="btn btn-primary text-uppercase font-ram font-size-14 font-weight-normal px-4 py-2 mb-2">
                                Visit the Blog
                            </a>
                        </div>
                    </div>
                </section>

                <!-- Photography Section -->
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

                <section class="photography px-4 py-5" id="photography">
                    <div class="text-center mb-5">
                        <h5 class="text-uppercase font-os font-size-16 text-muted">
                            Photography
                        </h5>
                        <h1 class="text-uppercase font-staat font-size-34">My Photography Work</h1>
                        <p class="font-ram font-size-16 text-black-50 mt-2">
                        Wildlife. Landscapes. Travel tales frozen in pixels.
                        </p>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div id="photographyCarousel" class="carousel slide shadow rounded-4 overflow-hidden" data-ride="carousel" data-interval="3500">
                                <div class="carousel-inner">
                                    <div class="carousel-item active">
                                        <img src="./assets/photos/DSC03302.png" class="d-block w-100 img-fluid rounded" style="aspect-ratio: 16/9; object-fit: cover;" alt="Photo 1">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="./assets/photos/DSC03471.jpg" class="d-block w-100 img-fluid rounded" style="aspect-ratio: 16/9; object-fit: cover;" alt="Photo 2">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="./assets/photos/DSC03550.JPG" class="d-block w-100 img-fluid rounded" style="aspect-ratio: 16/9; object-fit: cover;" alt="Photo 3">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="./assets/photos/DSC03307.jpg" class="d-block w-100 img-fluid rounded" style="aspect-ratio: 16/9; object-fit: cover;" alt="Photo 3">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="./assets/photos/DSC03529.JPG" class="d-block w-100 img-fluid rounded" style="aspect-ratio: 16/9; object-fit: cover;" alt="Photo 3">
                                    </div>
                                </div>
                                <!-- Add indicators for manual click navigation -->
                                <ol class="carousel-indicators">
                                    <li data-target="#photographyCarousel" data-slide-to="0" class="active"></li>
                                    <li data-target="#photographyCarousel" data-slide-to="1"></li>
                                    <li data-target="#photographyCarousel" data-slide-to="2"></li>
                                    <li data-target="#photographyCarousel" data-slide-to="3"></li>
                                    <li data-target="#photographyCarousel" data-slide-to="4"></li>
                                </ol>
                            </div>
                            <div class="text-center mt-4">
                                <p class="font-ram font-size-17 text-black-50 pb-3">
                                If these glimpses moved you, I invite you to explore the deeper journey through my lens.    
                                </p>
                                <a href="https://500px.com/p/pyd?view=photos" target="_blank"
                                  class="btn btn-primary text-uppercase font-ram font-size-14 font-weight-normal px-4 py-2 mb-2">
                                  View Full Portfolio
                                </a>
                            </div>
                        </div>
                    </div>
                </section>

                <!--footer-->
                <footer id="footer" class="pt-5 px-3">
                    <div id="contact_us">
                        <div class="row bg-light py-5">
                            <div class="col-sm-4 my-5 text-center">
                                <h6 class="font-ram font-size-16 text-black-50">&copy;2025 Puneeth. All rights reserved
                                </h6>
                            </div>
                            <div class="col-sm-4 my-4 text-center">
                                <div class="footer-title">
                                    <p class="description font-os font-size-16 text-black-50 text-uppercase">Made with ❤</p>
                                    <p class="font-ram font-weight-normal font-size-14 text-black-50 mt-2">
                                        Built as part of the AWS Cloud Resume Challenge
                                    </p>
                                </div>
                                <!-- <div class="counter-number">Couldn't read the counter</div> -->
                            </div>
                            <div class="social-icons col-sm-4 my-4 text-center">
                                <a href="https://www.linkedin.com/in/puneeth072003/" target="blank">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="https://github.com/puneeth072003" target="blank">
                                    <i class="fab fa-github"></i>
                                </a>
                                <a href="https://twitter.com/puneeth072003" target="blank">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>

            </div>
        </div>
    </main>

    <div id="greeting-toast" style="display:none; position:fixed; bottom:32px; right:32px; z-index:9999; background:#fff; border-radius:14px; box-shadow:0 4px 24px rgba(0,0,0,0.18); max-width:90vw; width:340px; padding:1.2rem 1.2rem 1rem 1.2rem; text-align:left; align-items:center; gap:1rem; flex-direction:row;">
      <div style="display:flex; align-items:center;">
        <img src="./assets/avatar-alt1.png" alt="Avatar" style="width:60px; height:60px; border-radius:50%; object-fit:cover; border:2px solid #444; margin-right:1rem;">
        <div>
          <div style="font-family:'Staatliches',cursive; font-size:1.3rem; color:#222;">Welcome!</div>
          <div style="font-family:'Rambla',sans-serif; font-size:1rem; color:#444;">
            Glad to have you.<br>
            <span id="greeting-visitor-number" style="font-weight:bold; color:#007bff;"></span>
          </div>
        </div>
      </div>
      <button id="greeting-toast-close" class="btn btn-primary" style="margin-top:0.7rem; float:right; font-size:0.9rem; padding:0.3rem 1.2rem;">Close</button>
    </div>

    <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/shoelace-autoloader.js"></script>
    <script src="https://code.jquery.com/jquery-3.4.1.slim.min.js"
        integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
        crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
        integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6"
        crossorigin="anonymous"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/js/all.min.js"
        integrity="sha256-MAgcygDRahs+F/Nk5Vz387whB4kSK9NXlDN3w58LLq0=" crossorigin="anonymous"></script>

    <script src="./vendor/typed/typed.min.js"></script>

    <script src="./index.js"></script>
</body>

</html>
